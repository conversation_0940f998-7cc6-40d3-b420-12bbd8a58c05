#!/bin/bash

# GameFlex Mobile - Staging Build Script for macOS
# This script builds the app for staging deployment

set -e  # Exit on any error

# Default values
PLATFORM="ios"
HELP=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -h|--help)
            HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Show help
if [ "$HELP" = true ]; then
    echo "GameFlex Mobile - Staging Build Script"
    echo ""
    echo "Usage: ./build-staging.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --platform PLATFORM  Target platform (ios, android, web, macos)"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./build-staging.sh                    # Build iOS staging"
    echo "  ./build-staging.sh -p android         # Build Android staging"
    echo "  ./build-staging.sh -p web             # Build Web staging"
    echo "  ./build-staging.sh -p macos           # Build macOS staging"
    exit 0
fi

echo "🚀 Building GameFlex Mobile for STAGING"
echo "Platform: $PLATFORM"
echo ""

# Ensure we're in the correct directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root."
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean
if [ $? -ne 0 ]; then
    echo "❌ Flutter clean failed"
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get
if [ $? -ne 0 ]; then
    echo "❌ Flutter pub get failed"
    exit 1
fi

# Build based on platform
PLATFORM_LOWER=$(echo "$PLATFORM" | tr '[:upper:]' '[:lower:]')
case "$PLATFORM_LOWER" in
    "ios")
        echo "🏗️ Building for iOS (staging)..."

        # Build using Flutter with staging configuration
        flutter build ios --release --dart-define=STAGING=true --config-only

        # Temporarily set Staging as default configuration for Xcode
        echo "🔧 Setting Staging as default configuration for Xcode..."
        sed -i.bak 's/defaultConfigurationName = Release;/defaultConfigurationName = Staging;/g' ios/Runner.xcodeproj/project.pbxproj
        sed -i.bak2 's/defaultConfigurationName = Debug;/defaultConfigurationName = Staging;/g' ios/Runner.xcodeproj/project.pbxproj

        # Also update the main Runner scheme to default to Staging for archiving
        echo "🔧 Setting main Runner scheme to use Staging for archiving..."
        sed -i.bak3 's/buildConfiguration = "Release"/buildConfiguration = "Staging"/g' ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme

        # Force Xcode to reload project settings
        touch ios/Runner.xcodeproj/project.pbxproj
        touch ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme

        # Build with Xcode using the staging scheme
        echo "📱 Building with Xcode using staging scheme..."
        cd ios
        xcodebuild -workspace Runner.xcworkspace -scheme Runner-Staging -configuration Staging -destination generic/platform=iOS build
        cd ..

        echo "✅ Staging build completed! Xcode is now set to default to Staging configuration for archiving."
        echo "💡 To restore Release as default, run: ./build-dev.sh -p ios"
        ;;
    "android")
        echo "🏗️ Building for Android (staging)..."
        flutter build apk --release --flavor staging --dart-define=STAGING=true
        ;;
    "web")
        echo "🏗️ Building for Web (staging)..."
        flutter build web --release --dart-define=STAGING=true
        ;;
    "macos")
        echo "🏗️ Building for macOS (staging)..."
        flutter build macos --release --dart-define=STAGING=true
        ;;
    "linux")
        echo "🏗️ Building for Linux (staging)..."
        flutter build linux --release --dart-define=STAGING=true
        ;;
    *)
        echo "❌ Unsupported platform: $PLATFORM"
        echo "Supported platforms: ios, android, web, macos, linux"
        exit 1
        ;;
esac

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo ""
echo "✅ Staging build completed successfully!"

# Show build output location
case "$PLATFORM_LOWER" in
    "ios")
        echo "📁 Build output: build/ios/iphoneos/Runner.app (or build/ios/iphonesimulator/Runner.app)"
        ;;
    "android")
        echo "📁 Build output: build/app/outputs/flutter-apk/app-staging-release.apk"
        ;;
    "web")
        echo "📁 Build output: build/web/"
        ;;
    "macos")
        echo "📁 Build output: build/macos/Build/Products/Release/gameflex_mobile.app"
        ;;
    "linux")
        echo "📁 Build output: build/linux/x64/release/bundle/"
        ;;
esac
