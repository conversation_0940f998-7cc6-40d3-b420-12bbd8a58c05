#!/bin/bash

# GameFlex Backend - Staging Data Seeding Script
# This script seeds the staging environment with test data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}🌱 $1${NC}"
    echo "=========================================="
}

print_header "GameFlex Backend - Staging Data Seeding"

# Set environment to staging
export ENVIRONMENT=staging

# Check if .env.staging exists
if [ ! -f ".env.staging" ]; then
    print_error ".env.staging file not found"
    print_status "Create it with staging configuration including secrets"
    print_status "Example: cp .env.staging.example .env.staging"
    exit 1
fi

print_status "Using staging environment configuration"

# Check AWS credentials
print_status "Validating AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    print_status "Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "AWS credentials validated"

# Run the seed script for staging
print_status "Starting staging data seeding..."
./scripts/seed-data.sh -e staging "$@"

if [ $? -eq 0 ]; then
    echo ""
    print_success "Staging data seeding completed successfully!"
    echo ""
    print_status "What was seeded:"
    echo "  • AWS Secrets Manager with app configuration"
    echo "  • DynamoDB tables with test data"
    echo "  • S3 bucket with test images"
    echo "  • Test users and channels"
    echo ""
    print_status "Staging endpoints:"
    echo "  • API: ${API_BASE_URL:-https://staging.api.gameflex.io}"
    echo "  • Media: https://staging.media.gameflex.io"
    echo ""
    print_status "Test the staging environment:"
    echo "  cd ../tests && npm test"
else
    print_error "Staging data seeding failed"
    exit 1
fi
