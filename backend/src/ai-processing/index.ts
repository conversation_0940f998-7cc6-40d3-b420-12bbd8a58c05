import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';
import { RekognitionClient, DetectModerationLabelsCommand, DetectLabelsCommand } from '@aws-sdk/client-rekognition';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, UpdateCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
// Configure AWS clients
const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-west-2' });
const rekognitionClient = new RekognitionClient({ region: process.env.AWS_REGION || 'us-west-2' });
const dynamodbClient = new DynamoDBClient({ region: process.env.AWS_REGION || 'us-west-2' });
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient, {
  marshallOptions: {
    removeUndefinedValues: true,
  },
});

// Environment variables
const MEDIA_TABLE = process.env.MEDIA_TABLE || 'gameflex-development-Media';
const USERS_TABLE = process.env.USERS_TABLE || 'gameflex-development-Users';
const BUCKET_NAME = process.env.BUCKET_NAME || 'gameflex-media-development';
const CLOUDFRONT_DOMAIN = process.env.CLOUDFRONT_DOMAIN || '';

// Helper function to get CloudFront URL
const getCloudFrontUrl = (s3Key: string): string => {
  if (CLOUDFRONT_DOMAIN) {
    return `https://${CLOUDFRONT_DOMAIN}/${s3Key}`;
  }
  // Fallback to S3 URL if CloudFront not configured
  return `https://${BUCKET_NAME}.s3.us-west-2.amazonaws.com/${s3Key}`;
};

// Types for AI analysis results
interface ModerationAnalysis {
  isInappropriate: boolean;
  confidence: number;
  moderationLabels: Array<{
    name: string;
    confidence: number;
    parentName?: string;
    categories?: string[];
    taxonomyLevel?: number; // 1, 2, 3
    instances?: Array<{
      boundingBox?: {
        width: number;
        height: number;
        left: number;
        top: number;
      };
      confidence: number;
    }>;
  }>;
  summary: string;
  blockedReasons?: string[]; // Specific reasons why content was blocked
}

interface ContentAnalysis {
  labels: Array<{
    name: string;
    confidence: number;
    categories?: string[];
    instances?: Array<{
      boundingBox?: {
        width: number;
        height: number;
        left: number;
        top: number;
      };
      confidence: number;
    }>;
    parents?: Array<{
      name: string;
    }>;
  }>;
  videoGameRelated: boolean;
  videoGameConfidence: number;
  suggestedTags: string[];
  dominantColors?: Array<{
    red: number;
    green: number;
    blue: number;
    cssColor: string;
    simplifiedColor: string;
    pixelPercent: number;
  }>;
  imageProperties?: {
    quality?: {
      brightness: number;
      sharpness: number;
      contrast: number;
    };
    dominant_colors?: any[];
    foreground?: {
      quality: {
        brightness: number;
        sharpness: number;
      };
      dominant_colors: any[];
    };
    background?: {
      quality: {
        brightness: number;
        sharpness: number;
      };
      dominant_colors: any[];
    };
  };
}

interface ProcessingResult {
  mediaId: string;
  status: 'processing' | 'approved' | 'failed' | 'rejected_inappropriate';
  contentType: string;
  moderationAnalysis?: ModerationAnalysis;
  contentAnalysis?: ContentAnalysis;
  metadata: {
    fileSize: number;
    fileType: string;
    dimensions?: { width: number; height: number };
    processedAt: string;
    processingTimeMs: number;
  };
  error?: string;
  rejectionReason?: string;
}

// Video game related keywords for classification
const VIDEO_GAME_KEYWORDS = [
  'game', 'gaming', 'video game', 'console', 'controller', 'joystick', 'gamepad',
  'xbox', 'playstation', 'nintendo', 'pc gaming', 'steam', 'esports', 'gaming setup',
  'monitor', 'keyboard', 'mouse', 'headset', 'character', 'avatar', 'weapon',
  'armor', 'fantasy', 'sci-fi', 'medieval', 'futuristic', 'digital art',
  'screenshot', 'gameplay', 'streamer', 'twitch', 'youtube gaming'
];

// Main Lambda handler for S3 events
export const handler: S3Handler = async (event: S3Event): Promise<void> => {
  console.log('AI Processing Lambda triggered:', JSON.stringify(event, null, 2));

  for (const record of event.Records) {
    if (record.eventName.startsWith('ObjectCreated')) {
      await processS3Object(record.s3.bucket.name, record.s3.object.key);
    }
  }
};

// Process individual S3 object
async function processS3Object(bucketName: string, objectKey: string): Promise<void> {
  const startTime = Date.now();
  console.log(`Processing object: ${objectKey} from bucket: ${bucketName}`);

  try {
    // Extract media ID from object key (assuming format: media/{mediaId}.{ext})
    const mediaId = extractMediaIdFromKey(objectKey);
    if (!mediaId) {
      console.log(`Skipping non-media object: ${objectKey}`);
      return;
    }

    // Get media record from DynamoDB
    const mediaRecord = await getMediaRecord(mediaId);
    if (!mediaRecord) {
      console.error(`Media record not found for ID: ${mediaId}`);
      return;
    }

    // Skip if already processed
    if (mediaRecord.status === 'approved' || mediaRecord.status === 'rejected_inappropriate') {
      console.log(`Media ${mediaId} already processed with status: ${mediaRecord.status}`);
      return;
    }

    // Update status to processing
    await updateMediaStatus(mediaId, 'processing');

    // Get object from S3
    const s3Object = await s3Client.send(new GetObjectCommand({
      Bucket: bucketName,
      Key: objectKey,
    }));

    if (!s3Object.Body) {
      throw new Error('S3 object body is empty');
    }

    // Convert stream to buffer
    const imageBuffer = await streamToBuffer(s3Object.Body as any);

    // Determine file type and size
    const fileType = s3Object.ContentType || 'image/jpeg';
    const fileSize = s3Object.ContentLength || imageBuffer.length;

    // Initialize processing result
    const result: ProcessingResult = {
      mediaId,
      status: 'processing',
      contentType: fileType,
      metadata: {
        fileSize,
        fileType,
        processedAt: new Date().toISOString(),
        processingTimeMs: 0,
      },
    };

    // Step 1: Content Moderation using AWS Rekognition
    console.log(`Running content moderation for media: ${mediaId}`);
    const moderationAnalysis = await detectInappropriateContent(imageBuffer);
    result.moderationAnalysis = moderationAnalysis;

    // Step 2: Content Analysis (always run to get metadata, even for rejected content)
    console.log(`Running content analysis for media: ${mediaId}`);
    const contentAnalysis = await analyzeContentAndTags(imageBuffer);
    result.contentAnalysis = contentAnalysis;

    // If content is inappropriate, move to review area for internal review
    if (moderationAnalysis.isInappropriate) {
      result.status = 'rejected_inappropriate';
      result.rejectionReason = `Content flagged as inappropriate: ${moderationAnalysis.summary} (confidence: ${moderationAnalysis.confidence}%)`;
      result.metadata.processingTimeMs = Date.now() - startTime;

      // Move the file from staging to review area for internal review
      await moveToReviewArea(bucketName, objectKey, mediaId, 'inappropriate');

      // Add demerit to user for inappropriate content
      const mediaRecord = await getMediaRecord(mediaId);
      if (mediaRecord && mediaRecord.user_id) {
        await addDemeritToUser(mediaRecord.user_id, `Inappropriate content: ${moderationAnalysis.summary}`, mediaId);
      }

      await updateMediaWithResults(mediaId, result);
      console.log(`Media ${mediaId} rejected as inappropriate and moved to review area: ${moderationAnalysis.summary}`);
      return;
    }

    // Content is appropriate - move from staging to final location
    console.log(`Content approved - moving from staging to final location for media: ${mediaId}`);
    await moveFromStagingToFinal(bucketName, objectKey, mediaId);

    // Mark as approved
    result.status = 'approved';
    result.metadata.processingTimeMs = Date.now() - startTime;

    // Update media record with results and new public URL
    await updateMediaWithResults(mediaId, result);

    // Store detailed analysis results in S3
    await storeAnalysisResults(bucketName, mediaId, result);

    console.log(`Successfully processed and approved media: ${mediaId} in ${result.metadata.processingTimeMs}ms`);

  } catch (error) {
    console.error(`Error processing object ${objectKey}:`, error);

    // Try to extract media ID and update with error
    const mediaId = extractMediaIdFromKey(objectKey);
    if (mediaId) {
      await updateMediaStatus(mediaId, 'failed', (error as Error).message);
    }
  }
}

// Extract media ID from S3 object key
function extractMediaIdFromKey(objectKey: string): string | null {
  // Expected format: media/{mediaId}.{ext} or {mediaId}.{ext}
  const match = objectKey.match(/(?:media\/)?([a-f0-9-]{36})\./);
  return match ? match[1] : null;
}

// Get media record from DynamoDB
async function getMediaRecord(mediaId: string): Promise<any> {
  try {
    const result = await dynamodb.send(new GetCommand({
      TableName: MEDIA_TABLE,
      Key: { id: mediaId },
    }));
    return result.Item;
  } catch (error) {
    console.error(`Error getting media record ${mediaId}:`, error);
    return null;
  }
}

// Update media status in DynamoDB
async function updateMediaStatus(mediaId: string, status: string, errorMessage?: string): Promise<void> {
  try {
    const updateExpression = errorMessage
      ? 'SET #status = :status, error_message = :error, updatedAt = :updatedAt'
      : 'SET #status = :status, updatedAt = :updatedAt';

    const expressionAttributeValues: any = {
      ':status': status,
      ':updatedAt': new Date().toISOString(),
    };

    if (errorMessage) {
      expressionAttributeValues[':error'] = errorMessage;
    }

    await dynamodb.send(new UpdateCommand({
      TableName: MEDIA_TABLE,
      Key: { id: mediaId },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: expressionAttributeValues,
    }));
  } catch (error) {
    console.error(`Error updating media status for ${mediaId}:`, error);
  }
}

// Update media record with full processing results
async function updateMediaWithResults(mediaId: string, result: ProcessingResult): Promise<void> {
  try {
    const updateExpression = `
      SET #status = :status,
          content_type = :contentType,
          moderation_analysis = :moderationAnalysis,
          content_analysis = :contentAnalysis,
          processing_completed_at = :completedAt,
          processing_time_ms = :processingTime,
          updatedAt = :updatedAt
    `;

    const expressionAttributeValues: any = {
      ':status': result.status,
      ':contentType': result.contentType,
      ':moderationAnalysis': result.moderationAnalysis || null,
      ':contentAnalysis': result.contentAnalysis || null,
      ':completedAt': result.metadata.processedAt,
      ':processingTime': result.metadata.processingTimeMs,
      ':updatedAt': new Date().toISOString(),
    };

    // Add rejection reason if content was rejected
    let finalUpdateExpression = updateExpression;
    if (result.rejectionReason) {
      finalUpdateExpression += ', rejection_reason = :rejectionReason';
      expressionAttributeValues[':rejectionReason'] = result.rejectionReason;
    }

    // Add error message if processing failed
    if (result.error) {
      finalUpdateExpression += ', error_message = :errorMessage';
      expressionAttributeValues[':errorMessage'] = result.error;
    }

    await dynamodb.send(new UpdateCommand({
      TableName: MEDIA_TABLE,
      Key: { id: mediaId },
      UpdateExpression: finalUpdateExpression,
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: expressionAttributeValues,
    }));

    console.log(`Successfully updated media ${mediaId} with processing results`);
  } catch (error) {
    console.error(`Error updating media with results for ${mediaId}:`, error);
  }
}

// Detect inappropriate content using AWS Rekognition
async function detectInappropriateContent(imageBuffer: Buffer): Promise<ModerationAnalysis> {
  try {
    const command = new DetectModerationLabelsCommand({
      Image: { Bytes: imageBuffer },
      MinConfidence: 50, // Minimum confidence threshold
    });

    const response = await rekognitionClient.send(command);
    const moderationLabels = response.ModerationLabels || [];

    console.log(`Detected ${moderationLabels.length} moderation labels`);

    // Process moderation labels with full metadata
    const inappropriateCategories = new Set<string>();
    const blockedReasons: string[] = [];

    const processedLabels = moderationLabels.map(label => {
      const labelName = label.Name || '';
      const parentName = label.ParentName || '';
      const confidence = label.Confidence || 0;

      // Add to inappropriate categories if confidence is high enough
      if (confidence > 70) {
        inappropriateCategories.add(parentName || labelName);
      }

      // Determine taxonomy level based on hierarchy
      let taxonomyLevel = 1; // Default to Level 1
      if (parentName) {
        taxonomyLevel = 2; // Has parent, so it's Level 2
        // Check if this could be Level 3 by looking for common L3 patterns
        if (labelName.includes('&') || labelName.includes('Gore') || labelName.includes('Injury')) {
          taxonomyLevel = 3;
        }
      }

      // Process instances if available (ModerationLabel doesn't have Instances, but we'll keep structure for consistency)
      const instances: any[] = [];

      return {
        name: labelName,
        confidence,
        ...(parentName && { parentName }),
        categories: parentName ? [parentName] : [], // Use parent as category
        taxonomyLevel,
        ...(instances.length > 0 && { instances }),
      };
    });

    // Determine if content should be rejected using blacklist approach
    // Block specific inappropriate categories while allowing general gaming content

    // Define blocked categories with taxonomy levels for real images
    const realImageBlockedCategories = new Map([
      // Level 1 - Always block explicit content
      ['Explicit Nudity', 1],

      // Level 2/3 - Block specific violent subcategories
      ['Physical Violence', 3],
      ['Blood & Gore', 3],
      ['Self-Harm', 3],

      // Level 2/3 - Block visually disturbing content
      ['Corpses', 3],

      // Level 1/2 - Block hate content
      ['Extremist', 2]
    ]);

    // Define blocked categories with taxonomy levels for animated/illustrated images
    const animatedImageBlockedCategories = new Map([
      // Level 1 - Always block explicit content (stricter for animated)
      ['Explicit Nudity', 1],

      // Level 2/3 - Block violent subcategories (more permissive for animated gaming)
      ['Blood & Gore', 3],
      ['Self-Harm', 2],

      // Level 2/3 - Block visually disturbing content
      ['Corpses', 3],
      ['Extremist', 2],
    ]);

    // Detect if image is animated/illustrated by checking for common indicators
    const isAnimatedOrIllustrated = moderationLabels.some(label => {
      const labelName = label.Name || '';
      const animatedIndicators = [
        'Cartoon', 'Animation', 'Anime', 'Illustrated', 'Drawing',
        'Sketch', 'Art', 'Painting', 'Digital Art', 'Comic'
      ];
      return animatedIndicators.some(indicator =>
        labelName.toLowerCase().includes(indicator.toLowerCase())
      );
    });

    // Select appropriate blocked categories based on image type
    const blockedCategories = isAnimatedOrIllustrated
      ? animatedImageBlockedCategories
      : realImageBlockedCategories;

    const inappropriateLabels = moderationLabels.filter(label => {
      const confidence = label.Confidence || 0;
      const labelName = label.Name || '';
      const parentName = label.ParentName || '';

      // Check if this label or its parent is in the blocked categories
      const isBlocked = blockedCategories.has(labelName) ||
        blockedCategories.has(parentName);

      // Track blocked reasons with taxonomy level
      if (confidence > 70 && isBlocked) {
        const blockedCategory = blockedCategories.has(labelName) ? labelName : parentName;
        const taxonomyLevel = blockedCategories.get(blockedCategory) || 0;
        const imageType = isAnimatedOrIllustrated ? 'Animated' : 'Real';
        blockedReasons.push(`${blockedCategory} [L${taxonomyLevel}] (${Math.round(confidence)}% confidence, ${imageType} image)`);
      }

      // Only reject if confidence > 70% AND it's a blocked category
      return confidence > 70 && isBlocked;
    });

    const isInappropriate = inappropriateLabels.length > 0;

    // Calculate overall confidence
    const confidence = moderationLabels.length > 0
      ? Math.max(...moderationLabels.map(label => label.Confidence || 0))
      : 0;

    // Generate summary of inappropriate content
    const summary = isInappropriate
      ? `Content blocked: ${blockedReasons.join(', ')}`
      : inappropriateCategories.size > 0
        ? `Contains: ${Array.from(inappropriateCategories).join(', ')} (allowed for gaming)`
        : 'Content appears appropriate';

    return {
      isInappropriate,
      confidence: Math.round(confidence),
      moderationLabels: processedLabels,
      summary,
      ...(blockedReasons.length > 0 && { blockedReasons }),
    };
  } catch (error) {
    console.error('Error in content moderation:', error);
    // Return safe result on error - reject content for safety
    return {
      isInappropriate: true,
      confidence: 100,
      moderationLabels: [],
      summary: 'Moderation check failed - content rejected for safety',
    };
  }
}

// Analyze content and generate tags using AWS Rekognition
async function analyzeContentAndTags(imageBuffer: Buffer): Promise<ContentAnalysis> {
  try {
    // Run multiple Rekognition analyses in parallel
    const [labelsResponse, imagePropertiesResponse] = await Promise.all([
      // Detect labels with detailed information
      rekognitionClient.send(new DetectLabelsCommand({
        Image: { Bytes: imageBuffer },
        MaxLabels: 100, // Increased for more comprehensive tagging
        MinConfidence: 60, // Slightly lower threshold for more tags
        Features: ['GENERAL_LABELS', 'IMAGE_PROPERTIES'],
        Settings: {
          GeneralLabels: {
            LabelInclusionFilters: undefined,
            LabelExclusionFilters: undefined,
            LabelCategoryInclusionFilters: undefined,
            LabelCategoryExclusionFilters: undefined,
          },
          ImageProperties: {
            MaxDominantColors: 10,
          },
        },
      })),

      // Get image properties for additional metadata
      rekognitionClient.send(new DetectLabelsCommand({
        Image: { Bytes: imageBuffer },
        Features: ['IMAGE_PROPERTIES'],
        Settings: {
          ImageProperties: {
            MaxDominantColors: 10,
          },
        },
      })).catch(() => null), // Don't fail if image properties aren't available
    ]);

    const labels = labelsResponse.Labels || [];
    console.log(`Detected ${labels.length} labels for content analysis`);

    // Process labels with enhanced information
    const processedLabels = labels.map(label => ({
      name: label.Name || '',
      confidence: Math.round(label.Confidence || 0),
      categories: label.Categories?.map(cat => cat.Name || '') || [],
      instances: label.Instances?.map(instance => ({
        boundingBox: instance.BoundingBox ? {
          width: instance.BoundingBox.Width || 0,
          height: instance.BoundingBox.Height || 0,
          left: instance.BoundingBox.Left || 0,
          top: instance.BoundingBox.Top || 0,
        } : undefined,
        confidence: Math.round(instance.Confidence || 0),
      })) || [],
      parents: label.Parents?.map(parent => ({
        name: parent.Name || '',
      })) || [],
    }));

    // Extract dominant colors if available
    let dominantColors: any[] = [];
    if (imagePropertiesResponse?.ImageProperties?.DominantColors) {
      dominantColors = imagePropertiesResponse.ImageProperties.DominantColors.map((color: any) => ({
        red: color.Red || 0,
        green: color.Green || 0,
        blue: color.Blue || 0,
        cssColor: color.CSSColor || '',
        simplifiedColor: color.SimplifiedColor || '',
        pixelPercent: Math.round((color.PixelPercent || 0) * 100) / 100,
      }));
    }

    // Extract image quality properties
    const imageProperties = imagePropertiesResponse?.ImageProperties ? {
      ...(imagePropertiesResponse.ImageProperties.Quality && {
        quality: {
          brightness: Math.round((imagePropertiesResponse.ImageProperties.Quality.Brightness || 0) * 100) / 100,
          sharpness: Math.round((imagePropertiesResponse.ImageProperties.Quality.Sharpness || 0) * 100) / 100,
          contrast: Math.round((imagePropertiesResponse.ImageProperties.Quality.Contrast || 0) * 100) / 100,
        }
      }),
      ...(imagePropertiesResponse.ImageProperties.Foreground && {
        foreground: {
          quality: {
            brightness: Math.round((imagePropertiesResponse.ImageProperties.Foreground.Quality?.Brightness || 0) * 100) / 100,
            sharpness: Math.round((imagePropertiesResponse.ImageProperties.Foreground.Quality?.Sharpness || 0) * 100) / 100,
          },
          dominant_colors: imagePropertiesResponse.ImageProperties.Foreground.DominantColors || [],
        }
      }),
      ...(imagePropertiesResponse.ImageProperties.Background && {
        background: {
          quality: {
            brightness: Math.round((imagePropertiesResponse.ImageProperties.Background.Quality?.Brightness || 0) * 100) / 100,
            sharpness: Math.round((imagePropertiesResponse.ImageProperties.Background.Quality?.Sharpness || 0) * 100) / 100,
          },
          dominant_colors: imagePropertiesResponse.ImageProperties.Background.DominantColors || [],
        }
      }),
    } : undefined;

    // Determine if content is video game related
    const videoGameRelated = isVideoGameRelated(processedLabels);
    const videoGameConfidence = calculateVideoGameConfidence(processedLabels);
    const suggestedTags = generateSuggestedTags(processedLabels);

    return {
      labels: processedLabels,
      videoGameRelated,
      videoGameConfidence,
      suggestedTags,
      dominantColors,
      ...(imageProperties && { imageProperties }),
    };
  } catch (error) {
    console.error('Error in content analysis:', error);
    return {
      labels: [],
      videoGameRelated: false,
      videoGameConfidence: 0,
      suggestedTags: [],
      dominantColors: [],
    };
  }
}

// Check if content is video game related
function isVideoGameRelated(labels: Array<{ name: string; confidence: number; categories?: string[] }>): boolean {
  return labels.some(label => {
    const labelText = label.name.toLowerCase();
    const categoryText = (label.categories || []).join(' ').toLowerCase();

    return VIDEO_GAME_KEYWORDS.some(keyword =>
      (labelText.includes(keyword.toLowerCase()) || categoryText.includes(keyword.toLowerCase()))
      && label.confidence > 75
    );
  });
}

// Calculate video game confidence score
function calculateVideoGameConfidence(labels: Array<{ name: string; confidence: number; categories?: string[] }>): number {
  const gameRelatedLabels = labels.filter(label => {
    const labelText = label.name.toLowerCase();
    const categoryText = (label.categories || []).join(' ').toLowerCase();

    return VIDEO_GAME_KEYWORDS.some(keyword =>
      labelText.includes(keyword.toLowerCase()) || categoryText.includes(keyword.toLowerCase())
    );
  });

  if (gameRelatedLabels.length === 0) return 0;

  const avgConfidence = gameRelatedLabels.reduce((sum, label) => sum + label.confidence, 0) / gameRelatedLabels.length;
  return Math.round(avgConfidence);
}

// Generate suggested tags based on labels
function generateSuggestedTags(labels: Array<{ name: string; confidence: number; categories?: string[] }>): string[] {
  // Create a comprehensive list of tags from labels and categories
  const allTags = new Set<string>();

  labels.forEach(label => {
    if (label.confidence > 75) {
      // Add the main label
      allTags.add(label.name.toLowerCase());

      // Add categories if they're specific enough
      (label.categories || []).forEach(category => {
        if (category.length > 3) { // Avoid very short/generic categories
          allTags.add(category.toLowerCase());
        }
      });
    }
  });

  // Convert to array and sort by relevance (prioritize gaming-related tags)
  const tagsArray = Array.from(allTags);
  const gamingTags = tagsArray.filter(tag =>
    VIDEO_GAME_KEYWORDS.some(keyword => tag.includes(keyword.toLowerCase()))
  );
  const otherTags = tagsArray.filter(tag =>
    !VIDEO_GAME_KEYWORDS.some(keyword => tag.includes(keyword.toLowerCase()))
  );

  // Return gaming tags first, then other tags, limited to 15 total
  return [...gamingTags, ...otherTags].slice(0, 15);
}

// Store detailed analysis results in S3
async function storeAnalysisResults(bucketName: string, mediaId: string, result: ProcessingResult): Promise<void> {
  try {
    const key = `metadata/analysis/${mediaId}.json`;
    await s3Client.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: JSON.stringify(result, null, 2),
      ContentType: 'application/json',
    }));
  } catch (error) {
    console.error(`Error storing analysis results for ${mediaId}:`, error);
  }
}

// Add demerit to user record for rejected content
async function addDemeritToUser(userId: string, reason: string, mediaId: string): Promise<void> {
  try {
    const demerit = {
      reason,
      timestamp: new Date().toISOString(),
      mediaId
    };

    await dynamodb.send(new UpdateCommand({
      TableName: USERS_TABLE,
      Key: { id: userId },
      UpdateExpression: 'SET demerits = list_append(if_not_exists(demerits, :empty_list), :demerit), updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':demerit': [demerit],
        ':empty_list': [],
        ':updatedAt': new Date().toISOString()
      }
    }));

    console.log(`Added demerit to user ${userId}: ${reason} (Media: ${mediaId})`);
  } catch (error) {
    console.error('Error adding demerit to user:', error);
  }
}

// Convert stream to buffer
async function streamToBuffer(stream: any): Promise<Buffer> {
  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

// Move file from staging to final location after approval
async function moveFromStagingToFinal(bucketName: string, stagingKey: string, mediaId: string): Promise<void> {
  try {
    // Get media record to find final destination
    const mediaRecord = await getMediaRecord(mediaId);
    if (!mediaRecord || !mediaRecord.final_s3_key) {
      throw new Error(`Media record not found or missing final S3 key for ${mediaId}`);
    }

    const finalKey = mediaRecord.final_s3_key;

    console.log(`Moving file from staging: ${stagingKey} to final: ${finalKey}`);

    // Copy from staging to final location
    await s3Client.send(new CopyObjectCommand({
      Bucket: bucketName,
      CopySource: `${bucketName}/${stagingKey}`,
      Key: finalKey,
      MetadataDirective: 'COPY',
    }));

    // Delete from staging
    await s3Client.send(new DeleteObjectCommand({
      Bucket: bucketName,
      Key: stagingKey,
    }));

    // Update media record with final location and public URL
    const finalUrl = getCloudFrontUrl(finalKey);
    await dynamodb.send(new UpdateCommand({
      TableName: MEDIA_TABLE,
      Key: { id: mediaId },
      UpdateExpression: 'SET s3Key = :finalKey, #url = :finalUrl, updatedAt = :updatedAt',
      ExpressionAttributeNames: { '#url': 'url' },
      ExpressionAttributeValues: {
        ':finalKey': finalKey,
        ':finalUrl': finalUrl,
        ':updatedAt': new Date().toISOString(),
      },
    }));

    console.log(`Successfully moved media ${mediaId} from staging to final location`);
  } catch (error) {
    console.error(`Error moving media ${mediaId} from staging to final:`, error);
    throw error;
  }
}

// Move file to review area (for rejected content that needs internal review)
async function moveToReviewArea(bucketName: string, stagingKey: string, mediaId: string, reviewType: 'inappropriate' | 'failed'): Promise<void> {
  try {
    // Generate review area key with timestamp for organization
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const reviewKey = `review/${reviewType}/${timestamp}/${mediaId}/${stagingKey.split('/').pop()}`;

    console.log(`Moving ${reviewType} content from staging to review area: ${stagingKey} -> ${reviewKey}`);

    // Copy from staging to review area
    await s3Client.send(new CopyObjectCommand({
      Bucket: bucketName,
      CopySource: `${bucketName}/${stagingKey}`,
      Key: reviewKey,
      MetadataDirective: 'COPY',
      TaggingDirective: 'REPLACE',
      Tagging: `ReviewType=${reviewType}&MediaId=${mediaId}&ReviewDate=${timestamp}&OriginalKey=${encodeURIComponent(stagingKey)}`,
    }));

    // Delete from staging after successful copy
    await s3Client.send(new DeleteObjectCommand({
      Bucket: bucketName,
      Key: stagingKey,
    }));

    // Update media record with review location
    await dynamodb.send(new UpdateCommand({
      TableName: MEDIA_TABLE,
      Key: { id: mediaId },
      UpdateExpression: 'SET review_s3_key = :reviewKey, review_type = :reviewType, review_date = :reviewDate, updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':reviewKey': reviewKey,
        ':reviewType': reviewType,
        ':reviewDate': timestamp,
        ':updatedAt': new Date().toISOString(),
      },
    }));

    console.log(`Successfully moved ${reviewType} content to review area: ${reviewKey}`);
  } catch (error) {
    console.error(`Error moving content to review area ${stagingKey}:`, error);
    // Don't throw here as the content is already marked as rejected
    // Log the error for manual intervention
    console.error(`MANUAL INTERVENTION REQUIRED: Failed to move ${stagingKey} to review area for media ${mediaId}`);
  }
}
