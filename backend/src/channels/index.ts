import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, Scan<PERSON>ommand, GetCommand, PutCommand, UpdateCommand, DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3 clients
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

// Environment variables
const CHANNELS_TABLE = process.env.CHANNELS_TABLE;
const CHANNEL_MEMBERS_TABLE = process.env.CHANNEL_MEMBERS_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;

// TypeScript interfaces
interface Channel {
    id: string;
    name: string;
    description?: string;
    ownerId: string;
    isPublic: boolean;
    isActive: boolean;
    memberCount: number;
    iconMediaId?: string;
    createdAt: string;
    updatedAt: string;
}

interface ChannelMember {
    channelId: string;
    userId: string;
    role: 'owner' | 'admin' | 'member';
    joinedAt: string;
}

interface User {
    id: string;
    username: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    avatarUrl?: string;
}

interface Media {
    id: string;
    url: string;
}

interface UserContext {
    userId: string;
    email?: string;
    username?: string;
}

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
};

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: corsHeaders,
    body: JSON.stringify(body)
});

// Helper function to get user from context
const getUserFromContext = (event: APIGatewayProxyEvent): UserContext => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return {
            userId: (event.requestContext.authorizer as any).userId,
            email: (event.requestContext.authorizer as any).email || null,
            username: (event.requestContext.authorizer as any).username || null
        };
    }
    throw new Error('User not authenticated');
};

// Get all channels with pagination
const getChannels = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const user = getUserFromContext(event);

        console.log(`Getting channels - limit: ${limit}, offset: ${offset}`);

        // Scan channels table (in production, consider using a GSI for better performance)
        const scanCommand = new ScanCommand({
            TableName: CHANNELS_TABLE,
            Limit: parseInt(limit),
            ExclusiveStartKey: offset !== '0' ? JSON.parse(Buffer.from(offset, 'base64').toString()) : undefined
        });

        const result = await dynamodb.send(scanCommand);

        // For each channel, check if user is a member and get owner info
        const channelsWithMembership = await Promise.all(
            (result.Items || []).map(async (channel: any) => {
                // Check if user is a member
                const getMembershipCommand = new GetCommand({
                    TableName: CHANNEL_MEMBERS_TABLE,
                    Key: {
                        channelId: channel.id,
                        userId: user.userId
                    }
                });

                const membershipResult = await dynamodb.send(getMembershipCommand);
                const isUserMember = !!membershipResult.Item;
                const userRole = (membershipResult.Item as ChannelMember)?.role || null;

                // Get owner information
                const getOwnerCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: channel.ownerId }
                });

                const ownerResult = await dynamodb.send(getOwnerCommand);
                const owner = ownerResult.Item as User;

                // Get channel icon from media table if iconMediaId exists
                let iconUrl: string | null = null;
                if (channel.iconMediaId) {
                    const getIconCommand = new GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: channel.iconMediaId }
                    });

                    const iconResult = await dynamodb.send(getIconCommand);
                    iconUrl = (iconResult.Item as Media)?.url || null;
                }

                return {
                    ...channel,
                    ownerUsername: owner?.username,
                    ownerDisplayName: owner?.displayName || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
                    ownerAvatarUrl: owner?.avatarUrl,
                    iconUrl: iconUrl,
                    isUserMember: isUserMember,
                    userRole: userRole
                };
            })
        );

        const nextOffset = result.LastEvaluatedKey
            ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
            : null;

        return createResponse(200, {
            channels: channelsWithMembership,
            hasMore: !!result.LastEvaluatedKey,
            nextOffset
        });

    } catch (error) {
        console.error('Error getting channels:', error);
        return createResponse(500, {
            error: 'Failed to get channels',
            details: (error as Error).message
        });
    }
};

// Get user's channels (channels they are a member of)
const getUserChannels = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const user = getUserFromContext(event);

        console.log(`Getting user channels for user: ${user.userId}`);

        // Query channel members table to get all channels this user is a member of
        // Note: This implementation uses scan with filter since we don't have a GSI yet
        // In production, consider adding a GSI on userId for better performance
        const scanCommand = new ScanCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            FilterExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': user.userId
            }
        });

        const membershipResult = await dynamodb.send(scanCommand);
        const memberships = membershipResult.Items || [];

        // Get channel details for each membership
        const userChannels = await Promise.all(
            memberships.map(async (membership: any) => {
                const channelMember = membership as ChannelMember;

                // Get channel details
                const getChannelCommand = new GetCommand({
                    TableName: CHANNELS_TABLE,
                    Key: { id: channelMember.channelId }
                });

                const channelResult = await dynamodb.send(getChannelCommand);
                const channel = channelResult.Item as Channel;

                if (!channel || !channel.isActive) {
                    return null; // Skip inactive channels
                }

                // Get owner information
                const getOwnerCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: channel.ownerId }
                });

                const ownerResult = await dynamodb.send(getOwnerCommand);
                const owner = ownerResult.Item as User;

                // Get channel icon from media table if iconMediaId exists
                let iconUrl: string | null = null;
                if (channel.iconMediaId) {
                    const getIconCommand = new GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: channel.iconMediaId }
                    });

                    const iconResult = await dynamodb.send(getIconCommand);
                    iconUrl = (iconResult.Item as Media)?.url || null;
                }

                return {
                    ...channel,
                    ownerUsername: owner?.username,
                    ownerDisplayName: owner?.displayName || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
                    ownerAvatarUrl: owner?.avatarUrl,
                    iconUrl: iconUrl,
                    isUserMember: true,
                    userRole: channelMember.role
                };
            })
        );

        // Filter out null values (inactive channels) and sort by name
        const activeUserChannels = userChannels
            .filter(channel => channel !== null)
            .sort((a, b) => a.name.localeCompare(b.name));

        console.log(`Returning ${activeUserChannels.length} user channels`);

        return createResponse(200, activeUserChannels);

    } catch (error) {
        console.error('Error getting user channels:', error);
        return createResponse(500, {
            error: 'Failed to get user channels',
            details: (error as Error).message
        });
    }
};

// Get posts for a specific channel
const getChannelPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }

        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const user = getUserFromContext(event);

        console.log(`Getting posts for channel: ${channelId} - limit: ${limit}, offset: ${offset}`);

        // First verify the channel exists and user has access
        const getChannelCommand = new GetCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        });

        const channelResult = await dynamodb.send(getChannelCommand);
        if (!channelResult.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }

        const channel = channelResult.Item as Channel;

        // Check if user is a member (for private channels)
        if (!channel.isPublic) {
            const getMembershipCommand = new GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channelId: channelId,
                    userId: user.userId
                }
            });

            const membershipResult = await dynamodb.send(getMembershipCommand);
            if (!membershipResult.Item) {
                return createResponse(403, { error: 'Access denied: You are not a member of this private channel' });
            }
        }

        // Scan posts table to find posts for this channel
        console.log(`Scanning posts table for channelId: ${channelId}`);
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'channelId = :channelId AND #status = :status AND active = :active',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':channelId': channelId,
                ':status': 'published',
                ':active': true
            },
            Limit: parseInt(limit)
        });

        const postsResult = await dynamodb.send(scanCommand);
        const posts = postsResult.Items || [];
        console.log(`Found ${posts.length} posts for channel ${channelId}`);

        // Sort posts by createdAt descending
        const sortedPosts = posts.sort((a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Get user information and media data for each post
        const postsWithCompleteInfo = await Promise.all(
            sortedPosts.map(async (post: any) => {
                // Get user information
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: post.authorId || post.userId }
                });

                const userResult = await dynamodb.send(getUserCommand);
                const postUser = userResult.Item as User;

                // Add user info to post
                post.username = postUser?.username;
                post.displayName = postUser?.displayName || `${postUser?.firstName || ''} ${postUser?.lastName || ''}`.trim();
                post.avatarUrl = postUser?.avatarUrl;

                // Fetch media data if post has mediaId or mediaId
                const mediaId = post.mediaId || post.mediaId;
                if (mediaId) {
                    try {
                        const getMediaCommand = new GetCommand({
                            TableName: MEDIA_TABLE,
                            Key: { id: mediaId }
                        });
                        const mediaResult = await dynamodb.send(getMediaCommand);

                        if (mediaResult.Item) {
                            post.media = mediaResult.Item;
                        }
                    } catch (error) {
                        console.error(`Failed to fetch media for post ${post.id}:`, error);
                        // Continue without media data if fetch fails
                    }
                }

                // Check if current user has liked this post
                let isLikedByCurrentUser = false;
                try {
                    const getLikeCommand = new GetCommand({
                        TableName: LIKES_TABLE,
                        Key: {
                            postId: post.id,
                            userId: user.userId
                        }
                    });
                    const likeResult = await dynamodb.send(getLikeCommand);

                    isLikedByCurrentUser = !!likeResult.Item;
                } catch (error) {
                    console.error(`Failed to check like status for post ${post.id}:`, error);
                    // Continue with false if check fails
                }

                post.isLikedByCurrentUser = isLikedByCurrentUser;

                return post;
            })
        );

        console.log(`Returning ${postsWithCompleteInfo.length} posts for channel: ${channelId}`);

        return createResponse(200, {
            posts: postsWithCompleteInfo,
            channelId: channelId,
            channelName: channel.name
        });

    } catch (error) {
        console.error('Error getting channel posts:', error);
        return createResponse(500, {
            error: 'Failed to get channel posts',
            details: (error as Error).message
        });
    }
};

// Create a new channel
const createChannel = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const user = getUserFromContext(event);

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const body = JSON.parse(event.body);
        const { name, description, isPublic = true } = body;

        if (!name || name.trim().length === 0) {
            return createResponse(400, { error: 'Channel name is required' });
        }

        const channelId = uuidv4();
        const now = new Date().toISOString();

        // Create channel
        const putChannelCommand = new PutCommand({
            TableName: CHANNELS_TABLE,
            Item: {
                id: channelId,
                name: name.trim(),
                description: description?.trim() || null,
                ownerId: user.userId,
                isPublic: isPublic,
                isActive: true,
                memberCount: 1, // Owner is automatically a member
                createdAt: now,
                updatedAt: now
            }
        });

        await dynamodb.send(putChannelCommand);

        // Add owner as a member with 'owner' role
        const putMemberCommand = new PutCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channelId: channelId,
                userId: user.userId,
                role: 'owner' as const,
                joinedAt: now
            }
        });

        await dynamodb.send(putMemberCommand);

        console.log(`Channel created: ${channelId} by user: ${user.userId}`);

        return createResponse(201, {
            id: channelId,
            name: name.trim(),
            description: description?.trim() || null,
            ownerId: user.userId,
            isPublic: isPublic,
            isActive: true,
            memberCount: 1,
            createdAt: now,
            updatedAt: now,
            isUserMember: true,
            userRole: 'owner'
        });

    } catch (error) {
        console.error('Error creating channel:', error);
        return createResponse(500, {
            error: 'Failed to create channel',
            details: (error as Error).message
        });
    }
};

// Get a specific channel
const getChannel = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }

        const user = getUserFromContext(event);

        const getChannelCommand = new GetCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        });

        const result = await dynamodb.send(getChannelCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }

        const channel = result.Item as Channel;

        // Check if user is a member
        const getMembershipCommand = new GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });

        const membershipResult = await dynamodb.send(getMembershipCommand);
        const isUserMember = !!membershipResult.Item;
        const userRole = (membershipResult.Item as ChannelMember)?.role || null;

        // Get owner information
        const getOwnerCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: channel.ownerId }
        });

        const ownerResult = await dynamodb.send(getOwnerCommand);
        const owner = ownerResult.Item as User;

        // Get channel icon from media table if iconMediaId exists
        let iconUrl: string | null = null;
        if (channel.iconMediaId) {
            const getIconCommand = new GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: channel.iconMediaId }
            });

            const iconResult = await dynamodb.send(getIconCommand);
            iconUrl = (iconResult.Item as Media)?.url || null;
        }

        return createResponse(200, {
            ...channel,
            ownerUsername: owner?.username,
            ownerDisplayName: owner?.displayName || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
            ownerAvatarUrl: owner?.avatarUrl,
            iconUrl: iconUrl,
            isUserMember: isUserMember,
            userRole: userRole
        });

    } catch (error) {
        console.error('Error getting channel:', error);
        return createResponse(500, {
            error: 'Failed to get channel',
            details: (error as Error).message
        });
    }
};

// Join a channel
const joinChannel = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }

        const user = getUserFromContext(event);

        // Check if channel exists and is public
        const getChannelCommand = new GetCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        });

        const channelResult = await dynamodb.send(getChannelCommand);

        if (!channelResult.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }

        const channel = channelResult.Item as Channel;

        if (!channel.isPublic) {
            return createResponse(403, { error: 'Cannot join private channel' });
        }

        // Check if user is already a member
        const getMembershipCommand = new GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });

        const membershipResult = await dynamodb.send(getMembershipCommand);

        if (membershipResult.Item) {
            return createResponse(400, { error: 'Already a member of this channel' });
        }

        // Add user as member
        const now = new Date().toISOString();
        const putMemberCommand = new PutCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channelId: channelId,
                userId: user.userId,
                role: 'member' as const,
                joinedAt: now
            }
        });

        await dynamodb.send(putMemberCommand);

        // Update member count
        const updateChannelCommand = new UpdateCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD memberCount :inc SET updatedAt = :now',
            ExpressionAttributeValues: {
                ':inc': 1,
                ':now': now
            }
        });

        await dynamodb.send(updateChannelCommand);

        console.log(`User ${user.userId} joined channel ${channelId}`);

        return createResponse(200, {
            message: 'Successfully joined channel',
            channelId: channelId,
            userRole: 'member'
        });

    } catch (error) {
        console.error('Error joining channel:', error);
        return createResponse(500, {
            error: 'Failed to join channel',
            details: (error as Error).message
        });
    }
};

// Leave a channel
const leaveChannel = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }

        const user = getUserFromContext(event);

        // Check if user is a member
        const getMembershipCommand = new GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });

        const membershipResult = await dynamodb.send(getMembershipCommand);

        if (!membershipResult.Item) {
            return createResponse(400, { error: 'Not a member of this channel' });
        }

        const membership = membershipResult.Item as ChannelMember;

        // Check if user is the owner
        if (membership.role === 'owner') {
            return createResponse(400, { error: 'Channel owner cannot leave channel' });
        }

        // Remove membership
        const deleteMemberCommand = new DeleteCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });

        await dynamodb.send(deleteMemberCommand);

        // Update member count
        const now = new Date().toISOString();
        const updateChannelCommand = new UpdateCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD memberCount :dec SET updatedAt = :now',
            ExpressionAttributeValues: {
                ':dec': -1,
                ':now': now
            }
        });

        await dynamodb.send(updateChannelCommand);

        console.log(`User ${user.userId} left channel ${channelId}`);

        return createResponse(200, {
            message: 'Successfully left channel',
            channel_id: channelId
        });

    } catch (error) {
        console.error('Error leaving channel:', error);
        return createResponse(500, {
            error: 'Failed to leave channel',
            details: (error as Error).message
        });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        const { httpMethod, resource } = event;

        switch (httpMethod) {
            case 'GET':
                if (resource === '/channels') {
                    return await getChannels(event);
                } else if (resource === '/channels/my') {
                    return await getUserChannels(event);
                } else if (resource === '/channels/{id}') {
                    return await getChannel(event);
                } else if (resource === '/channels/{id}/posts') {
                    return await getChannelPosts(event);
                }
                break;

            case 'POST':
                if (resource === '/channels') {
                    return await createChannel(event);
                } else if (resource === '/channels/{id}/join') {
                    return await joinChannel(event);
                } else if (resource === '/channels/{id}/leave') {
                    return await leaveChannel(event);
                }
                break;

            case 'PUT':
                if (resource === '/channels/{id}') {
                    // TODO: Implement updateChannel
                    return createResponse(501, { error: 'Update channel not implemented yet' });
                }
                break;

            case 'DELETE':
                if (resource === '/channels/{id}') {
                    // TODO: Implement deleteChannel
                    return createResponse(501, { error: 'Delete channel not implemented yet' });
                }
                break;

            default:
                return createResponse(405, { error: 'Method not allowed' });
        }

        return createResponse(404, { error: 'Endpoint not found' });

    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
