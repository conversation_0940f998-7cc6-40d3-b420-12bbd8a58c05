import { DynamoDBClient, GetItemCommand } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, ScanCommand, GetCommand, PutCommand, UpdateCommand, DeleteCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3 clients
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const s3 = new S3Client(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const USER_PROFILES_TABLE = process.env.USER_PROFILES_TABLE;
const FOLLOWS_TABLE = process.env.FOLLOWS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const AVATARS_BUCKET = process.env.AVATARS_BUCKET;
const XBOX_ACCOUNTS_TABLE = process.env.XBOX_ACCOUNTS_TABLE;

// TypeScript interfaces
interface User {
    id: string;
    email: string;
    username?: string;
    firstName?: string;
    lastName?: string;
    cognitoUserId: string;
    xboxUserId?: string;
    xboxGamertag?: string;
    xboxUserHash?: string;
    xboxXstsToken?: string;
    xboxTokenExpiry?: string;
    createdAt: string;
    updatedAt: string;
}

interface UserProfile {
    userId: string;
    bio?: string;
    avatarUrl?: string;
    location?: string;
    website?: string;
    followersCount?: number;
    followingCount?: number;
    createdAt?: string;
    updatedAt: string;
}

interface UserStats {
    postsCount: number;
    likesCount: number;
    followersCount: number;
    followingCount: number;
}

interface Follow {
    id: string;
    followerId: string;
    followingId: string;
    createdAt: string;
}

interface Like {
    postId: string;
    userId: string;
    post_id: string;
    user_id: string;
    createdAt: string;
}

interface XboxAccount {
    id: string;
    userId: string;
    xboxUserId: string;
    gamertag: string;
    xstsToken: string;
    userHash: string;
    microsoftRefreshToken?: string;
    profilePictureUrl?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    tokenExpiresAt: string;
}

interface LinkedAccount {
    type: 'xbox' | 'playstation' | 'steam' | 'discord';
    accountId: string;
    displayName: string;
    profilePictureUrl?: string;
    linkedAt: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// Helper function to get linked accounts for a user
const getLinkedAccounts = async (userId: string): Promise<LinkedAccount[]> => {
    const linkedAccounts: LinkedAccount[] = [];

    try {
        // Get Xbox account if linked
        const xboxQueryParams = {
            TableName: XBOX_ACCOUNTS_TABLE,
            IndexName: 'UserIdIndex',
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        };

        const xboxResult = await dynamodb.send(new QueryCommand(xboxQueryParams));

        if (xboxResult.Items && xboxResult.Items.length > 0) {
            const xboxAccount = xboxResult.Items[0] as XboxAccount;
            linkedAccounts.push({
                type: 'xbox',
                accountId: xboxAccount.xboxUserId,
                displayName: xboxAccount.gamertag,
                profilePictureUrl: xboxAccount.profilePictureUrl,
                linkedAt: xboxAccount.createdAt
            });
        }

        // TODO: Add other linked account types (PlayStation, Steam, Discord) here when implemented

    } catch (error) {
        console.error('Error getting linked accounts:', error);
        // Don't fail the entire request if linked accounts can't be retrieved
    }

    return linkedAccounts;
};

// Helper function to calculate user statistics
const calculateUserStats = async (userId: string): Promise<UserStats> => {
    try {
        console.log('calculateUserStats: Calculating stats for user ID:', userId);

        // Count posts by user
        const scanPostsCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            },
            Select: 'COUNT'
        });
        const postsResult = await dynamodb.send(scanPostsCommand);

        console.log('calculateUserStats: Found', postsResult.Count, 'posts for user', userId);

        // Count likes by user (using GSI)
        const queryLikesCommand = new QueryCommand({
            TableName: LIKES_TABLE,
            IndexName: 'userId-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const likesResult = await dynamodb.send(queryLikesCommand);

        console.log('calculateUserStats: Found', likesResult.Count, 'likes for user', userId);

        // Count followers
        const scanFollowersCommand = new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followingId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followersResult = await dynamodb.send(scanFollowersCommand);

        // Count following
        const scanFollowingCommand = new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followerId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followingResult = await dynamodb.send(scanFollowingCommand);

        const stats: UserStats = {
            postsCount: postsResult.Count || 0,
            likesCount: likesResult.Count || 0,
            followersCount: followersResult.Count || 0,
            followingCount: followingResult.Count || 0
        };

        console.log('calculateUserStats: Final stats for user', userId, ':', stats);

        return stats;
    } catch (error) {
        console.error('Error calculating user stats:', error);
        return {
            postsCount: 0,
            likesCount: 0,
            followersCount: 0,
            followingCount: 0
        };
    }
};

// Get user profile
const getProfile = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user from Users table
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        // Get user profile from UserProfiles table
        const getProfileCommand = new GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId }
        });
        const profileResult = await dynamodb.send(getProfileCommand);

        const user = userResult.Item as User;
        const profile = (profileResult.Item as UserProfile) || {};

        // Calculate real-time statistics
        const stats = await calculateUserStats(userId);

        // Get linked accounts
        const linkedAccounts = await getLinkedAccounts(userId);

        // Create display name from firstName and lastName, fallback to username
        const displayName = user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : user.username || 'Unknown User';

        return createResponse(200, {
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                display_name: displayName,
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            },
            profile: {
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || ''
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            },
            linkedAccounts: linkedAccounts
        });

    } catch (error) {
        console.error('GetProfile error:', error);
        return createResponse(500, { error: 'Failed to get profile', details: (error as Error).message });
    }
};

// Update user profile
const updateProfile = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { firstName, lastName, bio, location, website, avatarUrl } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Update Users table
        const userUpdateExpression: string[] = [];
        const userExpressionAttributeValues: Record<string, any> = {};

        if (firstName !== undefined) {
            userUpdateExpression.push('firstName = :firstName');
            userExpressionAttributeValues[':firstName'] = firstName;
        }

        if (lastName !== undefined) {
            userUpdateExpression.push('lastName = :lastName');
            userExpressionAttributeValues[':lastName'] = lastName;
        }

        userUpdateExpression.push('updatedAt = :updatedAt');
        userExpressionAttributeValues[':updatedAt'] = new Date().toISOString();

        if (userUpdateExpression.length > 1) { // More than just updatedAt
            const updateUserCommand = new UpdateCommand({
                TableName: USERS_TABLE,
                Key: { id: userId },
                UpdateExpression: `SET ${userUpdateExpression.join(', ')}`,
                ExpressionAttributeValues: userExpressionAttributeValues
            });
            await dynamodb.send(updateUserCommand);
        }

        // Update or create UserProfiles table entry
        const profileUpdateExpression: string[] = [];
        const profileExpressionAttributeValues: Record<string, any> = {};

        if (bio !== undefined) {
            profileUpdateExpression.push('bio = :bio');
            profileExpressionAttributeValues[':bio'] = bio;
        }

        if (location !== undefined) {
            profileUpdateExpression.push('#location = :location');
            profileExpressionAttributeValues[':location'] = location;
        }

        if (website !== undefined) {
            profileUpdateExpression.push('website = :website');
            profileExpressionAttributeValues[':website'] = website;
        }

        if (avatarUrl !== undefined) {
            profileUpdateExpression.push('avatarUrl = :avatarUrl');
            profileExpressionAttributeValues[':avatarUrl'] = avatarUrl;
        }

        profileUpdateExpression.push('updatedAt = :updatedAt');
        profileExpressionAttributeValues[':updatedAt'] = new Date().toISOString();

        if (profileUpdateExpression.length > 0) {
            const updateProfileCommand = new UpdateCommand({
                TableName: USER_PROFILES_TABLE,
                Key: { userId: userId },
                UpdateExpression: `SET ${profileUpdateExpression.join(', ')}`,
                ExpressionAttributeNames: location !== undefined ? { '#location': 'location' } : undefined,
                ExpressionAttributeValues: profileExpressionAttributeValues
            });
            await dynamodb.send(updateProfileCommand);
        }

        return createResponse(200, { message: 'Profile updated successfully' });

    } catch (error) {
        console.error('UpdateProfile error:', error);
        return createResponse(500, { error: 'Failed to update profile', details: (error as Error).message });
    }
};

// Get user by ID
const getUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const getProfileCommand = new GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id }
        });
        const profileResult = await dynamodb.send(getProfileCommand);

        const user = userResult.Item as User;
        const profile = (profileResult.Item as UserProfile) || {};

        // Calculate real-time statistics
        const stats = await calculateUserStats(id);

        // Get linked accounts
        const linkedAccounts = await getLinkedAccounts(id);

        // Check if current user is following this user
        const currentUserId = getUserIdFromContext(event);
        let isFollowing = false;
        if (currentUserId && currentUserId !== id) {
            const getFollowCommand = new GetCommand({
                TableName: FOLLOWS_TABLE,
                Key: { followerId: currentUserId, followingId: id }
            });
            const followResult = await dynamodb.send(getFollowCommand);
            isFollowing = !!followResult.Item;
        }

        // Create display name from firstName and lastName, fallback to username
        const displayName = user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : user.username || 'Unknown User';

        return createResponse(200, {
            user: {
                id: user.id,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                display_name: displayName,
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt
            },
            profile: {
                bio: profile.bio || '',
                avatar_url: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || ''
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            },
            linkedAccounts: linkedAccounts,
            isFollowing: isFollowing
        });

    } catch (error) {
        console.error('GetUser error:', error);
        return createResponse(500, { error: 'Failed to get user', details: (error as Error).message });
    }
};

// Follow user
const followUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // user to follow

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (userId === id) {
            return createResponse(400, { error: 'Cannot follow yourself' });
        }

        // Check if already following
        const getFollowCommand = new GetCommand({
            TableName: FOLLOWS_TABLE,
            Key: { followerId: userId, followingId: id }
        });
        const existingFollow = await dynamodb.send(getFollowCommand);

        if (existingFollow.Item) {
            return createResponse(400, { error: 'Already following this user' });
        }

        // Add follow relationship
        const putFollowCommand = new PutCommand({
            TableName: FOLLOWS_TABLE,
            Item: {
                followerId: userId,
                followingId: id,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putFollowCommand);

        // Update follower's following count
        const updateFollowerCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId },
            UpdateExpression: 'ADD followingCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowerCommand);

        // Update followed user's followers count
        const updateFollowedCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id },
            UpdateExpression: 'ADD followersCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowedCommand);

        return createResponse(200, { message: 'User followed successfully' });

    } catch (error) {
        console.error('FollowUser error:', error);
        return createResponse(500, { error: 'Failed to follow user', details: (error as Error).message });
    }
};

// Unfollow user
const unfollowUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // user to unfollow

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove follow relationship
        const deleteFollowCommand = new DeleteCommand({
            TableName: FOLLOWS_TABLE,
            Key: { followerId: userId, followingId: id }
        });
        await dynamodb.send(deleteFollowCommand);

        // Update follower's following count
        const updateFollowerCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId },
            UpdateExpression: 'ADD followingCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowerCommand);

        // Update followed user's followers count
        const updateFollowedCommand = new UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id },
            UpdateExpression: 'ADD followersCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowedCommand);

        return createResponse(200, { message: 'User unfollowed successfully' });

    } catch (error) {
        console.error('UnfollowUser error:', error);
        return createResponse(500, { error: 'Failed to unfollow user', details: (error as Error).message });
    }
};

// Get user's posts by user ID
const getUserPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }

        // Get pagination parameters
        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const limitNum = parseInt(limit);
        const offsetNum = parseInt(offset);

        // Get current user ID from authorizer context for like status
        const currentUserId = getUserIdFromContext(event);

        // Use scan with filter since we don't have a GSI yet
        // TODO: Replace with GSI query once userId-createdAt-index is added
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'userId = :userId AND #status = :status AND #active = :active',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#active': 'active'
            },
            ExpressionAttributeValues: {
                ':userId': id,
                ':status': 'published',
                ':active': true
            }
        });

        const result = await dynamodb.send(scanCommand);
        const allPosts = result.Items || [];

        // Sort posts by createdAt descending
        const sortedPosts = allPosts.sort((a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Apply pagination
        const paginatedPosts = sortedPosts.slice(offsetNum, offsetNum + limitNum);

        // Enrich posts with user data, media, and like status
        const enrichedPosts = await Promise.all(paginatedPosts.map(async (post: any) => {
            try {
                // Get user data
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: post.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);
                const user = userResult.Item;

                // Get media data if mediaId exists
                let media = null;
                if (post.mediaId) {
                    const mediaId = post.mediaId;
                    const getMediaCommand = new GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: mediaId }
                    });
                    const mediaResult = await dynamodb.send(getMediaCommand);
                    media = mediaResult.Item;
                }

                // Check if current user liked this post
                let isLikedByCurrentUser = false;
                if (currentUserId) {
                    const getLikeCommand = new GetCommand({
                        TableName: LIKES_TABLE,
                        Key: { postId: post.id, userId: currentUserId }
                    });
                    const likeResult = await dynamodb.send(getLikeCommand);
                    isLikedByCurrentUser = !!likeResult.Item;
                }

                return {
                    ...post,
                    user: user ? {
                        id: user.id,
                        username: user.username,
                        display_name: user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user.username || 'Unknown User',
                        avatar_url: null // Would need to get from UserProfiles table if needed
                    } : null,
                    media: media ? {
                        id: media.id,
                        url: media.url,
                        type: media.type,
                        thumbnailUrl: media.thumbnailUrl
                    } : null,
                    isLikedByCurrentUser: isLikedByCurrentUser,
                    likeCount: post.likes || 0,
                    commentCount: post.comments || 0,
                    reflexCount: post.reflexes || 0
                };
            } catch (error) {
                console.error(`Error enriching post ${post.id}:`, error);
                return post; // Return original post if enrichment fails
            }
        }));

        return createResponse(200, {
            posts: enrichedPosts,
            count: enrichedPosts.length,
            hasMore: offsetNum + limitNum < sortedPosts.length
        });

    } catch (error) {
        console.error('GetUserPosts error:', error);
        return createResponse(500, { error: 'Failed to get user posts', details: (error as Error).message });
    }
};

// Get user's liked posts
const getUserLikedPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const limit = parseInt(queryParams.limit || '20') || 20;
        const lastEvaluatedKey = queryParams.lastEvaluatedKey ? JSON.parse(decodeURIComponent(queryParams.lastEvaluatedKey)) : undefined;

        // Debug: Log the user ID being used
        console.log('getUserLikedPosts: Looking for likes for user ID:', userId);
        console.log('getUserLikedPosts: User ID type:', typeof userId);
        console.log('getUserLikedPosts: LIKES_TABLE:', LIKES_TABLE);
        console.log('getUserLikedPosts: lastEvaluatedKey:', lastEvaluatedKey);

        // Build query parameters
        const queryParams_db: any = {
            TableName: LIKES_TABLE,
            IndexName: 'userId-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false, // Sort by most recent first
            Limit: limit
        };

        // Add pagination if provided
        if (lastEvaluatedKey) {
            queryParams_db.ExclusiveStartKey = lastEvaluatedKey;
        }

        // Get user's likes using GSI
        const queryLikesCommand = new QueryCommand(queryParams_db);
        const likesResult = await dynamodb.send(queryLikesCommand);

        console.log('getUserLikedPosts: Found', likesResult.Items?.length || 0, 'likes for user', userId);
        console.log('getUserLikedPosts: Raw likes result:', JSON.stringify(likesResult.Items, null, 2));

        const likes = likesResult.Items || [];

        // Get post details for each like
        const likedPosts = await Promise.all(likes.map(async (like: any) => {
            try {
                // Get post details
                console.log('getUserLikedPosts: Getting post details for post ID:', like.postId);
                console.log('getUserLikedPosts: POSTS_TABLE:', POSTS_TABLE);
                console.log('getUserLikedPosts: DynamoDB get params:', JSON.stringify({
                    TableName: POSTS_TABLE,
                    Key: { id: like.postId }
                }, null, 2));
                console.log('getUserLikedPosts: AWS region:', process.env.AWS_REGION);

                // Use raw DynamoDB client (DocumentClient has issues in this function)
                const getPostCommand = new GetItemCommand({
                    TableName: POSTS_TABLE,
                    Key: marshall({ id: like.postId })
                });
                const rawPostResult = await dynamodbClient.send(getPostCommand);

                console.log('getUserLikedPosts: Raw DynamoDB result:', JSON.stringify(rawPostResult, null, 2));

                // Convert raw result to DocumentClient format
                const postResult = {
                    Item: rawPostResult.Item ? unmarshall(rawPostResult.Item) : null
                };

                if (!postResult.Item) {
                    return null; // Post might have been deleted
                }

                const post = postResult.Item;

                // Get user details for the post author using raw DynamoDB client
                // Use authorId or userId field (posts have both)
                const authorId = post.authorId || post.userId;
                console.log('getUserLikedPosts: Author ID:', authorId);

                let user: any = {};
                if (authorId) {
                    try {
                        const getUserCommand = new GetItemCommand({
                            TableName: USERS_TABLE,
                            Key: marshall({ id: authorId })
                        });
                        const rawUserResult = await dynamodbClient.send(getUserCommand);
                        user = rawUserResult.Item ? unmarshall(rawUserResult.Item) : {};
                        console.log('getUserLikedPosts: User lookup result:', JSON.stringify(user, null, 2));
                    } catch (userError) {
                        console.error('getUserLikedPosts: Error getting user details:', userError);
                        user = {}; // Use empty user object if lookup fails
                    }
                } else {
                    console.log('getUserLikedPosts: No author ID found for post');
                }

                return {
                    id: like.postId,
                    userId: authorId,
                    content: post.content,
                    mediaUrl: post.media_url,
                    mediaType: post.media_type,
                    likeCount: post.likes || 0,
                    commentCount: post.comments || 0,
                    createdAt: post.createdAt,
                    likedAt: like.createdAt,
                    username: user.username,
                    displayName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username,
                    avatarUrl: null, // Would need to get from UserProfiles table if needed
                    isLikedByCurrentUser: true // Always true since these are the user's likes
                };
            } catch (error) {
                console.error(`Error getting post details for like ${like.postId}:`, error);
                return null;
            }
        }));

        // Filter out null results (deleted posts)
        const validLikedPosts = likedPosts.filter(post => post !== null);

        // Prepare response with proper pagination
        const response: any = {
            posts: validLikedPosts,
            count: validLikedPosts.length,
            hasMore: !!likesResult.LastEvaluatedKey
        };

        // Include pagination token if there are more results
        if (likesResult.LastEvaluatedKey) {
            response.lastEvaluatedKey = encodeURIComponent(JSON.stringify(likesResult.LastEvaluatedKey));
        }

        return createResponse(200, response);

    } catch (error) {
        console.error('GetUserLikedPosts error:', error);
        return createResponse(500, { error: 'Failed to get liked posts', details: (error as Error).message });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'GET' && path === '/users/profile') {
            return await getProfile(event);
        } else if (httpMethod === 'PUT' && path === '/users/profile') {
            return await updateProfile(event);
        } else if (httpMethod === 'GET' && path === '/users/profile/liked-posts') {
            return await getUserLikedPosts(event);
        } else if (httpMethod === 'GET' && path.includes('/posts') && pathParameters && pathParameters.id) {
            return await getUserPosts(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getUser(event);
        } else if (httpMethod === 'POST' && path.includes('/follow')) {
            return await followUser(event);
        } else if (httpMethod === 'DELETE' && path.includes('/follow')) {
            return await unfollowUser(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};
