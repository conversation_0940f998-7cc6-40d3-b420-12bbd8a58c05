import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/post_model.dart';
import '../services/aws_posts_service.dart';
import '../services/aws_auth_service.dart';
import '../utils/app_logger.dart';

enum PostsStatus { initial, loading, loaded, error, refreshing }

class PostsProvider extends ChangeNotifier {
  PostsStatus _status = PostsStatus.initial;
  List<PostModel> _posts = [];
  String? _errorMessage;
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _pageSize = 20;

  // Current post index for maintaining scroll position
  int _currentPostIndex = 0;

  PostsStatus get status => _status;
  List<PostModel> get posts => _posts;
  String? get errorMessage => _errorMessage;
  bool get hasMore => _hasMore;
  bool get isLoading => _status == PostsStatus.loading;
  bool get isRefreshing => _status == PostsStatus.refreshing;
  int get currentPostIndex => _currentPostIndex;

  /// Update the current post index to maintain scroll position
  void setCurrentPostIndex(int index) {
    if (index >= 0 && index < _posts.length && _currentPostIndex != index) {
      _currentPostIndex = index;
      notifyListeners();
    }
  }

  /// Load initial posts
  Future<void> loadPosts() async {
    AppLogger.provider('loadPosts: Starting');
    if (_status == PostsStatus.loading) {
      AppLogger.provider('loadPosts: Already loading, returning');
      return;
    }

    // Check if user is authenticated before loading posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider('loadPosts: User not authenticated, skipping load');
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    AppLogger.provider('loadPosts: Setting status to loading');
    developer.log('PostsProvider: Starting to load posts');
    _setStatus(PostsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;

    try {
      AppLogger.provider('loadPosts: About to call AwsPostsService.getPosts()');
      developer.log('PostsProvider: Calling AwsPostsService.getPosts()');
      final posts = await AwsPostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );
      AppLogger.provider(
        'loadPosts: Received ${posts.length} posts from service',
      );

      developer.log('PostsProvider: Received ${posts.length} posts');
      AppLogger.provider('loadPosts: Setting posts in provider');
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on initial load
      AppLogger.provider('loadPosts: Setting status to loaded');
      _setStatus(PostsStatus.loaded);
      developer.log('PostsProvider: Successfully loaded posts');
      AppLogger.provider('loadPosts: Successfully completed');
    } catch (e, stackTrace) {
      AppLogger.error(
        'PostsProvider.loadPosts: Error occurred',
        error: e,
        stackTrace: stackTrace,
      );
      developer.log(
        'PostsProvider: Error loading posts',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to load posts: $e');
    }
  }

  /// Reset to top of feed immediately (for instant scroll response)
  void resetToTop() {
    AppLogger.provider('resetToTop: Immediately scrolling to top');
    _currentPostIndex = 0;
    notifyListeners();
  }

  /// Refresh posts (pull to refresh) - optimized for better UX
  Future<void> refreshPosts() async {
    if (_status == PostsStatus.refreshing) return;

    // Check if user is authenticated before refreshing posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'refreshPosts: User not authenticated, skipping refresh',
      );
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    developer.log('PostsProvider: Starting to refresh posts');
    AppLogger.provider(
      'REFRESH TRIGGERED - Background refresh for latest content',
    );

    // Don't change status to refreshing immediately - keep current posts visible
    // Only set refreshing if we have no posts to show
    if (_posts.isEmpty) {
      _setStatus(PostsStatus.refreshing);
    }

    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await AwsPostsService.instance.getPosts(
        limit: _pageSize,
        offset: 0,
      );

      developer.log('PostsProvider: Refreshed with ${posts.length} posts');
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on refresh
      _setStatus(PostsStatus.loaded);

      AppLogger.provider('REFRESH COMPLETED - ${posts.length} posts loaded');
    } catch (e, stackTrace) {
      developer.log(
        'PostsProvider: Error refreshing posts',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'PostsProvider REFRESH ERROR',
        error: e,
        stackTrace: stackTrace,
      );

      // Only show error if we have no posts to fall back to
      if (_posts.isEmpty) {
        _setError('Failed to refresh posts: $e');
      } else {
        // Keep existing posts and just log the error
        AppLogger.provider('Refresh failed but keeping existing posts');
      }
    }
  }

  /// Load more posts (pagination)
  Future<void> loadMorePosts() async {
    if (!_hasMore || _status == PostsStatus.loading) return;

    // Check if user is authenticated before loading more posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'loadMorePosts: User not authenticated, skipping load',
      );
      return;
    }

    try {
      final morePosts = await AwsPostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _posts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMore = morePosts.length == _pageSize;
        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more posts: $e');
    }
  }

  /// Like/unlike a post
  Future<void> toggleLike(String postId) async {
    try {
      developer.log('PostsProvider: Toggling like for post: $postId');

      // Find the post first
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) {
        developer.log('PostsProvider: Post not found in local list: $postId');
        return;
      }

      final post = _posts[postIndex];
      final wasLiked = post.isLikedByCurrentUser;

      // Optimistically update the UI first for immediate feedback
      final newLikeCount = wasLiked ? post.likeCount - 1 : post.likeCount + 1;
      final optimisticPost = post.copyWith(
        likeCount: newLikeCount,
        isLikedByCurrentUser: !wasLiked,
      );
      _posts[postIndex] = optimisticPost;
      notifyListeners();

      developer.log(
        'PostsProvider: Optimistically updated post. Was liked: $wasLiked, Old count: ${post.likeCount}, New count: $newLikeCount, Is liked: ${!wasLiked}',
      );

      // Then make the actual API call
      final success =
          wasLiked
              ? await AwsPostsService.instance.unlikePost(postId)
              : await AwsPostsService.instance.likePost(postId);
      developer.log('PostsProvider: API call completed. Success: $success');

      if (!success) {
        // Revert the optimistic update on failure
        _posts[postIndex] = post;
        notifyListeners();
        _setError('Failed to ${wasLiked ? 'unlike' : 'like'} post');
      } else {
        developer.log(
          'PostsProvider: Like/unlike completed successfully. Like count: ${optimisticPost.likeCount}, Is liked: ${optimisticPost.isLikedByCurrentUser}',
        );
      }
    } catch (e) {
      developer.log('PostsProvider: Error toggling like: $e');

      // Revert the optimistic update on error
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        // For now, just refresh all posts to get the correct state
        // In a production app, you might want to implement a single post refresh
        loadPosts();
      }

      _setError('Failed to like post: $e');
    }
  }

  /// Add a new post to the beginning of the list
  void addPost(PostModel post) {
    _posts.insert(0, post);
    notifyListeners();
  }

  /// Remove a post from the list
  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    notifyListeners();
  }

  /// Update a post in the list
  void updatePost(PostModel updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      notifyListeners();
    }
  }

  /// Refresh a specific post from the server
  Future<void> refreshPost(String postId) async {
    try {
      developer.log('PostsProvider: Refreshing post: $postId');

      // Find the post in our local list
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) {
        developer.log('PostsProvider: Post not found in local list: $postId');
        return;
      }

      // Try to get the updated post from the server
      final updatedPost = await AwsPostsService.instance.getPost(postId);
      if (updatedPost != null) {
        // Update the specific post in our list
        _posts[postIndex] = updatedPost;
        notifyListeners();
        developer.log('PostsProvider: Successfully refreshed post: $postId');
      } else {
        developer.log(
          'PostsProvider: Failed to get updated post, falling back to full refresh',
        );
        // Fallback to refreshing all posts if single post fetch fails
        await refreshPosts();
      }
    } catch (e) {
      developer.log('PostsProvider: Error refreshing post $postId: $e');
      // Fallback to refreshing all posts on error
      try {
        await refreshPosts();
      } catch (refreshError) {
        developer.log(
          'PostsProvider: Error in fallback refresh: $refreshError',
        );
        // Don't show error to user for background refresh
      }
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    if (_status == PostsStatus.error) {
      _setStatus(PostsStatus.loaded);
    }
  }

  /// Set status and notify listeners
  void _setStatus(PostsStatus status) {
    _status = status;
    if (status != PostsStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    developer.log('PostsProvider: Setting error: $error');
    AppLogger.error('PostsProvider: ERROR SET: $error');
    _errorMessage = error;
    _status = PostsStatus.error;
    notifyListeners();
  }

  /// Reset the provider state
  void reset() {
    _status = PostsStatus.initial;
    _posts.clear();
    _errorMessage = null;
    _hasMore = true;
    _currentOffset = 0;
    notifyListeners();
  }

  /// Start real-time subscriptions (not implemented for AWS backend)
  void startRealtimeSubscriptions() {
    developer.log(
      'PostsProvider: Real-time subscriptions not implemented for AWS backend',
    );
  }

  /// Stop real-time subscriptions (not implemented for AWS backend)
  void stopRealtimeSubscriptions() {
    developer.log(
      'PostsProvider: Real-time subscriptions not implemented for AWS backend',
    );
  }

  @override
  void dispose() {
    stopRealtimeSubscriptions();
    super.dispose();
  }
}
