import 'package:flutter/material.dart';
import '../models/reflex_model.dart';
import '../theme/app_theme.dart';

class ReflexFloatingCard extends StatefulWidget {
  final ReflexModel reflex;
  final bool isCenter;
  final bool isLoaded;
  final VoidCallback onTap;

  const ReflexFloatingCard({
    super.key,
    required this.reflex,
    required this.isCenter,
    this.isLoaded = false,
    required this.onTap,
  });

  @override
  State<ReflexFloatingCard> createState() => _ReflexFloatingCardState();
}

class _ReflexFloatingCardState extends State<ReflexFloatingCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  String? _cachedMediaUrl;
  bool _isLoadingUrl = false;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    // Load media URL once
    _loadMediaUrl();
  }

  Future<void> _loadMediaUrl() async {
    if (_isLoadingUrl || _cachedMediaUrl != null) return;

    if (widget.reflex.reflexType == ReflexType.customImage &&
        widget.reflex.mediaId != null) {
      setState(() {
        _isLoadingUrl = true;
      });

      try {
        final url = await widget.reflex.getMediaUrl();
        if (mounted) {
          setState(() {
            _cachedMediaUrl = url;
            _isLoadingUrl = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingUrl = false;
          });
        }
      }
    }
  }

  @override
  void didUpdateWidget(ReflexFloatingCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isCenter != oldWidget.isCenter) {
      if (widget.isCenter) {
        _scaleController.forward();
      } else {
        _scaleController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        final scale = widget.isCenter ? _scaleAnimation.value : 0.8;
        final opacity = widget.isCenter ? 1.0 : 0.7;

        return Transform.scale(
          scale: scale,
          child: Opacity(
            opacity: opacity,
            child: GestureDetector(
              onTap: widget.onTap,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                child: Stack(
                  children: [
                    // Main card with glow effect
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          // Main shadow
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.4),
                            blurRadius: 25,
                            spreadRadius: 5,
                            offset: const Offset(0, 12),
                          ),
                          // GameFlex glow effect
                          BoxShadow(
                            color: AppColors.gfGreen.withValues(alpha: 0.3),
                            blurRadius: 30,
                            spreadRadius: 2,
                            offset: const Offset(0, 0),
                          ),
                          // Inner glow
                          BoxShadow(
                            color: AppColors.gfTeal.withValues(alpha: 0.2),
                            blurRadius: 15,
                            spreadRadius: 1,
                            offset: const Offset(0, 0),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: AspectRatio(
                          aspectRatio: 9 / 16,
                          child: _buildReflexContent(),
                        ),
                      ),
                    ),
                    // User info overlay at top
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.7),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: _buildUserInfo(),
                      ),
                    ),
                    // Stats overlay at bottom
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20),
                          ),
                          gradient: LinearGradient(
                            begin: Alignment.bottomCenter,
                            end: Alignment.topCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.7),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: _buildStats(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReflexContent() {
    if (widget.reflex.reflexType == ReflexType.customImage &&
        widget.reflex.mediaId != null) {
      // For custom image reflexes, show the edited image
      if (_isLoadingUrl) {
        return Container(
          color: AppColors.gfDarkBackground,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          ),
        );
      }

      if (_cachedMediaUrl != null && _cachedMediaUrl!.isNotEmpty) {
        return Image.network(
          _cachedMediaUrl!,
          key: ValueKey(
            'floating_reflex_image_${widget.reflex.id}_$_cachedMediaUrl',
          ),
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: AppColors.gfDarkBackground,
              child: Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.gfGreen,
                  ),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.gfDarkBackground,
              child: const Center(
                child: Icon(Icons.error_outline, color: Colors.red, size: 48),
              ),
            );
          },
        );
      }

      return Container(
        color: AppColors.gfDarkBackground,
        child: const Center(
          child: Icon(Icons.error_outline, color: Colors.red, size: 48),
        ),
      );
    } else {
      // For flare reflexes, show a gradient background with flare effects
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.gfGreen.withValues(alpha: 0.3),
              AppColors.gfDarkBlue.withValues(alpha: 0.8),
              Colors.black,
            ],
          ),
        ),
        child: Stack(
          children: [
            // Flare effects placeholder
            _buildFlareEffects(),
            // Text overlay if available
            if (widget.reflex.textOverlay != null)
              Center(
                child: Text(
                  widget.reflex.textOverlay!,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black,
                        blurRadius: 10,
                        offset: Offset(2, 2),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      );
    }
  }

  Widget _buildFlareEffects() {
    // TODO: Implement actual flare effects based on widget.reflex.flareData
    // For now, show some animated particles
    return Stack(
      children: [
        Positioned(top: 100, left: 50, child: _buildFlareParticle()),
        Positioned(top: 200, right: 80, child: _buildFlareParticle()),
        Positioned(bottom: 150, left: 100, child: _buildFlareParticle()),
      ],
    );
  }

  Widget _buildFlareParticle() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.gfGreen,
        boxShadow: [
          // Main glow
          BoxShadow(
            color: AppColors.gfGreen.withValues(alpha: 0.8),
            blurRadius: 15,
            spreadRadius: 3,
          ),
          // Outer glow
          BoxShadow(
            color: AppColors.gfTeal.withValues(alpha: 0.4),
            blurRadius: 25,
            spreadRadius: 5,
          ),
          // Inner highlight
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.3),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        // User avatar
        widget.reflex.avatarUrl != null && widget.reflex.avatarUrl!.isNotEmpty
            ? CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.gfGrayBorder,
              backgroundImage: NetworkImage(widget.reflex.avatarUrl!),
            )
            : CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.gfGrayBorder,
              child: Text(
                (widget.reflex.displayName ?? widget.reflex.username ?? 'U')
                    .substring(0, 1)
                    .toUpperCase(),
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        const SizedBox(width: 12),
        // User name
        Expanded(
          child: Text(
            widget.reflex.displayName ?? widget.reflex.username ?? 'Unknown',
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: Colors.black,
                  blurRadius: 5,
                  offset: Offset(1, 1),
                ),
              ],
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStats() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Likes count with GameFlex shield
        Row(
          children: [
            Icon(
              Icons.shield,
              color: AppColors.gfGreen,
              size: 20,
              shadows: const [
                Shadow(
                  color: Colors.black,
                  blurRadius: 3,
                  offset: Offset(1, 1),
                ),
              ],
            ),
            const SizedBox(width: 4),
            Text(
              widget.reflex.likes.toString(),
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 5,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ),
        // Emoji reactions summary
        if (widget.reflex.reactions.isNotEmpty)
          Row(
            children:
                widget.reflex.reactions.entries
                    .take(3) // Show max 3 emoji types
                    .map(
                      (entry) => Padding(
                        padding: const EdgeInsets.only(left: 4),
                        child: Row(
                          children: [
                            Text(
                              entry.key,
                              style: const TextStyle(fontSize: 16),
                            ),
                            if (entry.value > 1)
                              Text(
                                entry.value.toString(),
                                style: const TextStyle(
                                  color: AppColors.gfOffWhite,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black,
                                      blurRadius: 5,
                                      offset: Offset(1, 1),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
          ),
      ],
    );
  }
}
