import 'dart:developer' as developer;
import '../services/api_service.dart';
import '../utils/app_logger.dart';

class UsernameValidationResult {
  final bool isValid;
  final bool isAvailable;
  final String? error;
  final String? details;
  final String? normalizedUsername;

  const UsernameValidationResult({
    required this.isValid,
    required this.isAvailable,
    this.error,
    this.details,
    this.normalizedUsername,
  });

  bool get isValidAndAvailable => isValid && isAvailable;
}

class UsernameValidator {
  static const int minLength = 4;
  static const int maxLength = 32;
  static final RegExp _validCharacters = RegExp(r'^[a-zA-Z0-9_-]+$');

  /// Performs local validation checks on the username
  static UsernameValidationResult validateLocally(String username) {
    final trimmedUsername = username.trim();

    // Check if empty
    if (trimmedUsername.isEmpty) {
      return const UsernameValidationResult(
        isValid: false,
        isAvailable: false,
        error: 'Username is required',
      );
    }

    // Check length
    if (trimmedUsername.length < minLength) {
      return UsernameValidationResult(
        isValid: false,
        isAvailable: false,
        error: 'Username too short',
        details: 'Username must be at least $minLength characters long',
      );
    }

    if (trimmedUsername.length > maxLength) {
      return UsernameValidationResult(
        isValid: false,
        isAvailable: false,
        error: 'Username too long',
        details: 'Username must be $maxLength characters or less',
      );
    }

    // Check valid characters
    if (!_validCharacters.hasMatch(trimmedUsername)) {
      return const UsernameValidationResult(
        isValid: false,
        isAvailable: false,
        error: 'Invalid characters',
        details:
            'Username can only contain letters, numbers, underscores, and hyphens',
      );
    }

    return UsernameValidationResult(
      isValid: true,
      isAvailable: true, // We don't know availability yet
      normalizedUsername: trimmedUsername.toLowerCase(),
    );
  }

  /// Checks username availability and validation with the backend
  static Future<UsernameValidationResult> checkAvailability(
    String username,
  ) async {
    try {
      developer.log('UsernameValidator: Checking availability for: $username');
      AppLogger.info('Checking username availability for: $username');

      // First do local validation
      final localValidation = validateLocally(username);
      if (!localValidation.isValid) {
        return localValidation;
      }

      // Make API request to check availability
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/auth/check-username-availability',
        queryParams: {'username': username.trim()},
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('UsernameValidator: API response: $data');
      AppLogger.info('Username availability response: $data');

      final isAvailable = data['available'] as bool? ?? false;
      final isValid = data['valid'] as bool? ?? false;
      final error = data['error'] as String?;
      final details = data['details'] as String?;
      final normalizedUsername = data['username'] as String?;

      return UsernameValidationResult(
        isValid: isValid,
        isAvailable: isAvailable,
        error: error,
        details: details,
        normalizedUsername: normalizedUsername,
      );
    } catch (e) {
      developer.log('UsernameValidator: Error checking availability: $e');
      AppLogger.error('Username availability check failed', error: e);

      return const UsernameValidationResult(
        isValid: false,
        isAvailable: false,
        error: 'Connection error',
        details: 'Unable to check username availability. Please try again.',
      );
    }
  }

  /// Gets a user-friendly error message for display
  static String getErrorMessage(UsernameValidationResult result) {
    if (result.isValidAndAvailable) {
      return '';
    }

    if (result.error != null) {
      if (result.details != null) {
        return '${result.error}: ${result.details}';
      }
      return result.error!;
    }

    if (!result.isValid) {
      return 'Invalid username format';
    }

    if (!result.isAvailable) {
      return 'Username is already taken';
    }

    return 'Unknown error';
  }

  /// Gets a success message when username is valid and available
  static String getSuccessMessage(UsernameValidationResult result) {
    if (result.isValidAndAvailable && result.normalizedUsername != null) {
      return 'Username "${result.normalizedUsername}" is available!';
    }
    return '';
  }
}
